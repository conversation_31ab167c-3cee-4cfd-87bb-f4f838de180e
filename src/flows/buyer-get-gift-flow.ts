import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { completePurchaseByBot } from "../firebase-service";
import { clearUserSession } from "../services/session";
import {
  getBusinessConnectionId,
  getOrderById,
  transferGift,
} from "../utils/business-connection-helpers";
import {
  logBuyerGiftRequestStarted,
  logOrderNotFoundForBuyerRequest,
  logUserNotAuthorizedBuyer,
  logOrderNotReadyForGiftDelivery,
  logNoGiftAvailableForBuyer,
  logMissingBusinessConnectionForBuyer,
  logGiftTransferToBuyerStarted,
  logGiftTransferToBuyerCompleted,
} from "./loggers/buyer-get-gift-flow.logger";
import { OrderStatus } from "../mikerudenko/marketplace-shared";

export interface FlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
  pendingOrderId: string;
}

export const handleBuyerGetGift = async (
  flowContext: FlowContext
): Promise<boolean> => {
  const { ctx, chat_id, userId, pendingOrderId } = flowContext;

  logBuyerGiftRequestStarted({
    chat_id,
    userId,
    pendingOrderId,
  });

  const existingOrder = await getOrderById(pendingOrderId);
  if (!existingOrder) {
    logOrderNotFoundForBuyerRequest({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.NO_ORDER_FOR_PROCESSING
    );
    return false;
  }

  if (existingOrder.buyer_tg_id !== userId) {
    logUserNotAuthorizedBuyer({
      chat_id,
      userId,
      pendingOrderId,
      ...(existingOrder.buyer_tg_id && {
        originalBuyerId: existingOrder.buyer_tg_id,
      }),
    });
    await ctx.telegram.sendMessage(
      chat_id,
      "You are not authorized to request this gift. Only the original buyer can claim the gift."
    );
    return false;
  }

  // Check if gift is ready (status should be 'gift_sent_to_relayer')
  if (existingOrder.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    logOrderNotReadyForGiftDelivery({
      chat_id,
      userId,
      pendingOrderId,
      orderStatus: existingOrder.status,
    });
    return false;
  }

  const giftToTransferToBuyer = existingOrder.gift?.owned_gift_id;

  if (!giftToTransferToBuyer) {
    logNoGiftAvailableForBuyer({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.NO_GIFT_TO_TRANSFER
    );
    return false;
  }

  const businessConnectionId = getBusinessConnectionId(ctx);
  if (!businessConnectionId) {
    logMissingBusinessConnectionForBuyer({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_MISSING_INFO
    );
    return false;
  }

  logGiftTransferToBuyerStarted({
    chat_id,
    userId,
    pendingOrderId,
    businessConnectionId,
    giftToTransferToBuyer,
  });

  await transferGift(ctx, businessConnectionId, chat_id, giftToTransferToBuyer);
  await completePurchaseByBot(pendingOrderId);
  await clearUserSession(userId);

  logGiftTransferToBuyerCompleted({
    chat_id,
    userId,
    pendingOrderId,
  });

  return true;
};
