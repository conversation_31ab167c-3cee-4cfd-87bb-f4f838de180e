import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';

interface UserOrderImageSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
}

export function UserOrderImageSection({
  collection,
  order,
}: UserOrderImageSectionProps) {
  return (
    <div className="relative">
      <div
        className={cn(
          'aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] border border-[#3a4a5c]/50 p-8',
          order.gift && 'p-0',
        )}
      >
        {order.gift ? (
          <TgsOrImageGift
            isImage={false}
            gift={order.gift}
            className="w-full h-full"
            style={{ width: '100%', height: '100%' }}
          />
        ) : collection ? (
          <TgsOrImage
            isImage={false}
            collectionId={collection.id}
            imageProps={{
              alt: collection.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        ) : (
          <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
            <div className="w-16 h-16 bg-[#17212b] rounded" />
          </div>
        )}
      </div>
    </div>
  );
}
