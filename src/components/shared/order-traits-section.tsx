'use client';

import type { OrderGift } from '@/mikerudenko/marketplace-shared';

interface OrderTraitsSectionProps {
  orderGift: OrderGift;
}

interface TraitRowProps {
  name: string;
  value: string;
  rarity: string;
}

function TraitRow({ name, value, rarity }: TraitRowProps) {
  return (
    <div className="flex justify-between items-center py-2">
      <span className="text-[#708499] text-sm capitalize">{name}</span>
      <span className="text-[#f5f5f5] text-sm font-medium">
        {value} <span className="text-[#6ab2f2]">({rarity})</span>
      </span>
    </div>
  );
}

export function OrderTraitsSection({ orderGift }: OrderTraitsSectionProps) {
  const formatRarity = (rarityPerMille: number): string => {
    const percentage = (rarityPerMille / 10).toFixed(1);
    return `${percentage}%`;
  };

  const traits = [
    {
      name: 'backdrop',
      value: orderGift.backdrop.name,
      rarity: formatRarity(orderGift.backdrop.rarity_per_mille),
    },
    {
      name: 'model',
      value: orderGift.model.name,
      rarity: formatRarity(orderGift.model.rarity_per_mille),
    },
    {
      name: 'symbol',
      value: orderGift.symbol.name,
      rarity: formatRarity(orderGift.symbol.rarity_per_mille),
    },
  ];

  return (
    <div className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4">
      <div className="space-y-1">
        {traits.map((trait) => (
          <TraitRow
            key={trait.name}
            name={trait.name}
            value={trait.value}
            rarity={trait.rarity}
          />
        ))}
      </div>
    </div>
  );
}
