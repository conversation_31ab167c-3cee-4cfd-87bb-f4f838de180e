'use client';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useEffect, useState } from 'react';

import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import { getImagePathWithFallback, isUserPidor } from '@/lib/utils';

const TgsViewer = dynamic(
  () => import('./tgs-viewer').then((mod) => ({ default: mod.TgsViewer })),
  {
    ssr: false,
    loading: () => (
      <TgsSkeleton
        className="w-full h-full"
        style={{ height: '200px', width: '200px' }}
      />
    ),
  },
);

enum TgsFallbackState {
  CDN_TGS = 'cdn_tgs',
  LOCAL_TGS = 'local_tgs',
  CDN_PNG = 'cdn_png',
  LOCAL_PNG = 'local_png',
  FAILED = 'failed',
}

enum ImageFallbackState {
  LOCAL_PNG = 'local_png',
  CDN_PNG = 'cdn_png',
  FAILED = 'failed',
}

function useImageFallbackChain(collectionId: string) {
  const [currentState, setCurrentState] = useState<ImageFallbackState>(
    ImageFallbackState.LOCAL_PNG,
  );
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!collectionId) {
      setCurrentSrc('');
      setCurrentState(ImageFallbackState.LOCAL_PNG);
      setIsLoading(false);
      return;
    }

    setCurrentState(ImageFallbackState.LOCAL_PNG);
    setIsLoading(true);

    const { fallback: localPng } = getImagePathWithFallback(
      collectionId,
      'png',
    );
    setCurrentSrc(localPng);
  }, [collectionId]);

  const handleError = () => {
    switch (currentState) {
      case ImageFallbackState.LOCAL_PNG: {
        setCurrentState(ImageFallbackState.CDN_PNG);
        setIsLoading(true);
        const { primary: cdnPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(cdnPng);
        break;
      }
      case ImageFallbackState.CDN_PNG:
        setCurrentState(ImageFallbackState.FAILED);
        setCurrentSrc('');
        setIsLoading(false);
        break;
      default:
        break;
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return {
    src: currentSrc,
    isFailed: currentState === ImageFallbackState.FAILED,
    isLoading,
    onError: handleError,
    onLoad: handleLoad,
    currentState,
  };
}

function useTgsFallbackChain(collectionId: string) {
  const [currentState, setCurrentState] = useState<TgsFallbackState>(
    TgsFallbackState.CDN_TGS,
  );
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isImage, setIsImage] = useState<boolean>(false);

  useEffect(() => {
    if (!collectionId) {
      setCurrentSrc('');
      setCurrentState(TgsFallbackState.CDN_TGS);
      setIsImage(false);
      return;
    }

    setCurrentState(TgsFallbackState.CDN_TGS);
    setIsImage(false);

    const { primary: cdnTgs } = getImagePathWithFallback(collectionId, 'tgs');
    setCurrentSrc(cdnTgs);
  }, [collectionId]);

  const handleError = () => {
    switch (currentState) {
      case TgsFallbackState.CDN_TGS: {
        setCurrentState(TgsFallbackState.LOCAL_TGS);
        setIsImage(false);
        const { fallback: localTgs } = getImagePathWithFallback(
          collectionId,
          'tgs',
        );
        setCurrentSrc(localTgs);
        break;
      }

      case TgsFallbackState.LOCAL_TGS: {
        setCurrentState(TgsFallbackState.CDN_PNG);
        setIsImage(true);
        const { primary: cdnPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(cdnPng);
        break;
      }

      case TgsFallbackState.CDN_PNG: {
        setCurrentState(TgsFallbackState.LOCAL_PNG);
        setIsImage(true);
        const { fallback: localPng } = getImagePathWithFallback(
          collectionId,
          'png',
        );
        setCurrentSrc(localPng);
        break;
      }

      case TgsFallbackState.LOCAL_PNG:
        setCurrentState(TgsFallbackState.FAILED);
        setCurrentSrc('');
        setIsImage(false);
        break;

      default:
        break;
    }
  };

  return {
    src: currentSrc,
    isImage,
    isFailed: currentState === TgsFallbackState.FAILED,
    onError: handleError,
    currentState,
  };
}

interface TgsOrImageProps {
  isImage: boolean;
  collectionId: string;
  imageProps?: {
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: 'lazy' | 'eager';
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
}

export function TgsOrImage({
  isImage,
  collectionId,
  imageProps,
  tgsProps,
}: TgsOrImageProps) {
  const imageFallback = useImageFallbackChain(collectionId);
  const tgsFallback = useTgsFallbackChain(collectionId);

  if (!collectionId) {
    return null;
  }

  const shouldForceImage = isImage || isUserPidor();

  if (shouldForceImage && imageProps) {
    if (imageFallback.isFailed || !imageFallback.src) {
      return (
        <div
          className={`flex items-center justify-center bg-gray-100 rounded-lg ${imageProps.className || ''}`}
          style={{ width: '100%', height: '200px' }}
        >
          <div className="text-center p-4">
            <div className="text-gray-500 text-sm">⚠️</div>
            <div className="text-xs text-gray-600">Image not available</div>
          </div>
        </div>
      );
    }

    return (
      <Image
        src={imageFallback.src}
        alt={imageProps.alt}
        className={imageProps.className}
        loading={imageProps.loading}
        sizes={
          imageProps.sizes ||
          (imageProps.fill
            ? '(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw'
            : undefined)
        }
        fill={imageProps.fill}
        width={imageProps.fill ? undefined : 200}
        height={imageProps.fill ? undefined : 200}
        style={{ display: 'block' }}
        onLoad={() => {
          imageFallback.onLoad();
        }}
        onError={(e) => {
          imageFallback.onError();
          imageProps.onError?.(e);
        }}
      />
    );
  }

  if (!shouldForceImage && tgsProps) {
    if (tgsFallback.isFailed || !tgsFallback.src) {
      return (
        <div
          className={`flex items-center justify-center bg-gray-100 rounded-lg ${tgsProps.className || ''}`}
          style={tgsProps.style}
        >
          <div className="text-center p-4">
            <div className="text-gray-500 text-sm">⚠️</div>
            <div className="text-xs text-gray-600">Media not available</div>
          </div>
        </div>
      );
    }

    if (tgsFallback.isImage) {
      return (
        <Image
          src={tgsFallback.src}
          alt={imageProps?.alt || 'Collection image'}
          className={tgsProps.className}
          style={tgsProps.style}
          width={200}
          height={200}
          onError={tgsFallback.onError}
        />
      );
    }

    return (
      <TgsViewer
        tgsUrl={tgsFallback.src}
        style={tgsProps.style}
        onError={tgsFallback.onError}
        className={tgsProps.className}
      />
    );
  }

  return null;
}
