import { useIntl } from 'react-intl';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  firebaseTimestampToDate,
  type UserTxEntity,
} from '@/mikerudenko/marketplace-shared';
import { useTransactionTypeText } from '@/services/transaction-type-service';
import {
  formatAmount,
  formatTransactionDate,
  getAmountColor,
} from '@/utils/transaction-utils';

import { desktopTransactionTableMessages } from './intl/desktop-transaction-table.messages';

interface DesktopTransactionTableProps {
  transactions: UserTxEntity[];
}

export function DesktopTransactionTable({
  transactions,
}: DesktopTransactionTableProps) {
  const { formatMessage: t } = useIntl();
  const getTransactionTypeText = useTransactionTypeText();

  return (
    <div className="hidden md:block bg-[#232e3c] border border-[#3a4a5c] rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-[#3a4a5c]">
            <TableHead className="text-[#f5f5f5]">
              {t(desktopTransactionTableMessages.type)}
            </TableHead>
            <TableHead className="text-[#f5f5f5]">
              {t(desktopTransactionTableMessages.amount)}
            </TableHead>
            <TableHead className="text-[#f5f5f5]">
              {t(desktopTransactionTableMessages.date)}
            </TableHead>
            <TableHead className="text-[#f5f5f5]">
              {t(desktopTransactionTableMessages.description)}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow
              key={transaction.id}
              className="border-[#3a4a5c] hover:bg-[#2a3441]"
            >
              <TableCell className="text-[#f5f5f5]">
                {getTransactionTypeText(transaction.tx_type)}
              </TableCell>
              <TableCell className={getAmountColor(transaction.amount)}>
                {formatAmount(transaction.amount)}
              </TableCell>
              <TableCell className="text-[#708499]">
                {formatTransactionDate(
                  firebaseTimestampToDate(transaction.createdAt),
                )}
              </TableCell>
              <TableCell className="text-[#708499] max-w-xs truncate">
                {transaction.description || '-'}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
