import * as admin from "firebase-admin";
import { applyTransactionSign } from "../../utils/transaction-sign-utils";
import {
  APP_USERS_COLLECTION,
  AppDate,
  TxType,
  UserTxEntity,
} from "../../mikerudenko/marketplace-shared";
import {
  logTransactionCreated,
  logTransactionHistoryError,
} from "./transaction-history-service.logger";

const USER_TX_HISTORY_SUBCOLLECTION = "tx_history";

export interface CreateTransactionParams {
  userId: string;
  txType: TxType;
  amount: number;
  orderId?: string;
  description?: string;
  isReceivingCompensation?: boolean; // For CANCELATION_FEE only
}

export async function createTransactionRecord(
  params: CreateTransactionParams
): Promise<void> {
  const {
    userId,
    txType,
    amount,
    orderId,
    description,
    isReceivingCompensation,
  } = params;

  try {
    // Apply standardized sign to the amount
    const signedAmount = applyTransactionSign(
      amount,
      txType,
      isReceivingCompensation
    );

    const db = admin.firestore();
    const txRecord: UserTxEntity = {
      tx_type: txType,
      user_id: userId,
      amount: signedAmount,
      order_id: orderId,
      description,
      createdAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    };

    // Store transaction as sub-collection under the user
    await db
      .collection(APP_USERS_COLLECTION)
      .doc(userId)
      .collection(USER_TX_HISTORY_SUBCOLLECTION)
      .add(txRecord);

    logTransactionCreated({
      userId,
      txType,
      amount: signedAmount,
    });
  } catch (error) {
    logTransactionHistoryError({
      error,
      operation: "create_transaction_record",
      userId,
      txType,
    });
    // Don't throw error to avoid breaking main operations
    // Transaction history is supplementary data
  }
}
