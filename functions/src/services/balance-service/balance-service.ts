import * as admin from "firebase-admin";
import { roundToThreeDecimals, safeSubtract } from "../../utils";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";
import {
  logBalanceUpdate,
  logInsufficientFunds,
  logFundsValidated,
  logBalanceServiceError,
} from "./balance-service.logger";
import {
  UserEntity,
  UserBalance,
  TxType,
  APP_USERS_COLLECTION,
} from "../../mikerudenko/marketplace-shared";

export async function getUserBalance(userId: string) {
  try {
    const db = admin.firestore();
    const userDoc = await db.collection(APP_USERS_COLLECTION).doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserEntity;
    return userData.balance || { sum: 0, locked: 0 };
  } catch (error) {
    logBalanceServiceError({
      error,
      operation: "get_user_balance",
      userId,
    });
    throw error;
  }
}

export async function updateUserBalance(params: {
  userId: string;
  sumChange: number;
  lockedChange: number;
}) {
  const { userId, sumChange, lockedChange } = params;
  try {
    const db = admin.firestore();
    const userRef = db.collection(APP_USERS_COLLECTION).doc(userId);

    return await db.runTransaction(
      async (transaction: admin.firestore.Transaction) => {
        const userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw new Error(`User ${userId} not found`);
        }

        const userData = userDoc.data() as UserEntity;
        const currentBalance: UserBalance = userData.balance || {
          sum: 0,
          locked: 0,
        };

        // Calculate new balances
        const newSum = roundToThreeDecimals(currentBalance.sum + sumChange);
        const newLocked = roundToThreeDecimals(
          currentBalance.locked + lockedChange
        );

        // Validate that balances won't go negative
        if (newSum < 0) {
          throw new Error(
            `Insufficient balance: current ${currentBalance.sum} TON, attempting to change by ${sumChange} TON`
          );
        }

        if (newLocked < 0) {
          throw new Error(
            `Insufficient locked funds: current ${currentBalance.locked} TON, attempting to change by ${lockedChange} TON`
          );
        }

        // Validate that locked amount doesn't exceed total balance
        if (newLocked > newSum) {
          throw new Error(
            `Locked amount (${newLocked} TON) cannot exceed total balance (${newSum} TON)`
          );
        }

        const newBalance: UserBalance = {
          sum: newSum,
          locked: newLocked,
        };

        transaction.update(userRef, { balance: newBalance });

        logBalanceUpdate({
          userId,
          sumChange,
          lockedChange,
          operation: "balance_update",
        });

        return newBalance;
      }
    );
  } catch (error) {
    logBalanceServiceError({
      error,
      operation: "update_user_balance",
      userId,
    });
    throw error;
  }
}

export async function addFunds(userId: string, amount: number) {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  return await updateUserBalance({
    userId,
    sumChange: amount,
    lockedChange: 0,
  });
}

export async function lockFunds(userId: string, amount: number) {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);
  const availableBalance = safeSubtract(
    currentBalance.sum,
    currentBalance.locked
  );

  if (availableBalance < amount) {
    throw new Error(
      `Insufficient available balance to lock ${amount} TON. Available: ${availableBalance} TON`
    );
  }

  return await updateUserBalance({
    userId,
    sumChange: 0,
    lockedChange: amount,
  });
}

export async function unlockFunds(userId: string, amount: number) {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);

  if (currentBalance.locked < amount) {
    throw new Error(
      `Insufficient locked funds to unlock ${amount} TON. Locked: ${currentBalance.locked} TON`
    );
  }

  return await updateUserBalance({
    userId,
    sumChange: 0,
    lockedChange: -amount,
  });
}

export async function spendLockedFunds(userId: string, amount: number) {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);

  if (currentBalance.locked < amount) {
    throw new Error(
      `Insufficient locked funds to spend ${amount} TON. Locked: ${currentBalance.locked} TON`
    );
  }

  if (currentBalance.sum < amount) {
    throw new Error(
      `Insufficient total balance to spend ${amount} TON. Balance: ${currentBalance.sum} TON`
    );
  }

  return await updateUserBalance({
    userId,
    sumChange: -amount,
    lockedChange: -amount,
  });
}

export async function hasAvailableBalance(userId: string, amount: number) {
  try {
    const balance = await getUserBalance(userId);
    const availableBalance = safeSubtract(balance.sum, balance.locked);
    return availableBalance >= amount;
  } catch (error) {
    logBalanceServiceError({
      error,
      operation: "check_available_balance",
      userId,
    });
    return false;
  }
}

export async function validateSufficientFunds(params: {
  userId: string;
  amount: number;
  operation: string;
}) {
  const { userId, amount, operation } = params;
  const balance = await getUserBalance(userId);
  const availableBalance = safeSubtract(balance.sum, balance.locked);

  if (availableBalance < amount) {
    logInsufficientFunds({
      userId,
      requiredAmount: amount,
      availableBalance,
      operation,
    });
    throw new Error(
      `Insufficient funds for ${operation}: required ${amount} TON, available ${availableBalance} TON (total: ${balance.sum} TON, locked: ${balance.locked} TON)`
    );
  }

  logFundsValidated({ userId, amount, operation });
}

export async function validateSufficientLockedFunds(
  userId: string,
  amount: number,
  operation: string = "operation"
) {
  const balance = await getUserBalance(userId);

  if (balance.locked < amount) {
    throw new Error(
      `Insufficient locked funds for ${operation}: required ${amount} TON, locked ${balance.locked} TON`
    );
  }
}

export async function addFundsWithHistory(params: {
  userId: string;
  amount: number;
  txType: TxType;
  orderId?: string;
  description?: string;
  isReceivingCompensation?: boolean;
}) {
  const {
    userId,
    amount,
    txType,
    orderId,
    description,
    isReceivingCompensation,
  } = params;

  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const result = await updateUserBalance({
    userId,
    sumChange: amount,
    lockedChange: 0,
  });

  // Record transaction history with correct sign
  await createTransactionRecord({
    userId,
    txType,
    amount,
    orderId,
    description,
    isReceivingCompensation,
  });

  return result;
}

export async function lockFundsWithHistory(params: {
  userId: string;
  amount: number;
  txType: TxType;
  orderId?: string;
  description?: string;
}) {
  const { userId, amount, txType, orderId, description } = params;

  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);
  const availableBalance = safeSubtract(
    currentBalance.sum,
    currentBalance.locked
  );

  if (availableBalance < amount) {
    throw new Error(
      `Insufficient available balance to lock ${amount} TON. Available: ${availableBalance} TON`
    );
  }

  const result = await updateUserBalance({
    userId,
    sumChange: 0,
    lockedChange: amount,
  });

  // Record transaction history with correct sign
  await createTransactionRecord({
    userId,
    txType,
    amount,
    orderId,
    description,
  });

  return result;
}

export async function unlockFundsWithHistory(params: {
  userId: string;
  amount: number;
  txType: TxType;
  orderId?: string;
  description?: string;
}) {
  const { userId, amount, txType, orderId, description } = params;

  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);

  if (currentBalance.locked < amount) {
    throw new Error(
      `Insufficient locked funds to unlock ${amount} TON. Locked: ${currentBalance.locked} TON`
    );
  }

  const result = await updateUserBalance({
    userId,
    sumChange: 0,
    lockedChange: -amount,
  });

  // Record transaction history with correct sign
  await createTransactionRecord({
    userId,
    txType,
    amount,
    orderId,
    description,
  });

  return result;
}

export async function spendFundsWithHistory(params: {
  userId: string;
  amount: number;
  txType: TxType;
  orderId?: string;
  description?: string;
}) {
  const { userId, amount, txType, orderId, description } = params;

  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  const currentBalance = await getUserBalance(userId);
  const availableBalance = safeSubtract(
    currentBalance.sum,
    currentBalance.locked
  );

  if (availableBalance < amount) {
    throw new Error(
      `Insufficient available balance to spend ${amount} TON. Available: ${availableBalance} TON`
    );
  }

  const result = await updateUserBalance({
    userId,
    sumChange: -amount,
    lockedChange: 0,
  });

  // Record transaction history with correct sign
  await createTransactionRecord({
    userId,
    txType,
    amount,
    orderId,
    description,
  });

  return result;
}
