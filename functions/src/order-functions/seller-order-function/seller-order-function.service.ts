import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { processPurchase } from "../../services/purchase-flow-service";

export interface CreateOrderAsSellerParams {
  sellerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsSellerParams {
  sellerId: string;
  orderId: string;
}

export class SellerOrderFunctionService {
  static validateCreateOrderRequest(
    request: any,
    params: CreateOrderAsSellerParams
  ): any {
    const authRequest = requireAuthentication(request);
    validateOrderCreationParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);
    return authRequest;
  }

  static validatePurchaseRequest(
    request: any,
    params: MakePurchaseAsSellerParams
  ): any {
    const authRequest = requireAuthentication(request);
    validatePurchaseParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);
    return authRequest;
  }

  static async createSellerOrder(
    params: CreateOrderAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, collectionId, price } = params;

    return await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      gift: null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });
  }

  static async processSellerPurchase(
    params: MakePurchaseAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, orderId } = params;

    return await processPurchase(db, {
      userId: sellerId,
      orderId,
      userType: UserType.SELLER,
    });
  }
}
