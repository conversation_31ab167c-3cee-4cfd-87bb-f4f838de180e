/* eslint-disable indent */
import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  setSecondaryMarketPrice as setSecondaryPriceService,
  makeSecondaryMarketPurchase as makeSecondaryPurchaseService,
} from "./secondary-market-function.service";
import {
  throwUnauthenticated,
  throwInvalidOrderId,
  throwInvalidSecondaryPrice,
  throwSecondaryMarketInternalError,
} from "./secondary-market-function.error-handler";
import {
  logSetSecondaryPriceStarted,
  logSetSecondaryPriceSuccess,
  logSecondaryPurchaseStarted,
  logSecondaryPurchaseSuccess,
  logSecondaryMarketError,
} from "./secondary-market-function.logger";

export const setSecondaryMarketPrice = onCall<{
  orderId: string;
  secondaryMarketPrice: number;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, secondaryMarketPrice } = request.data;
  const userId = request.auth?.uid;

  if (!userId) {
    throwUnauthenticated();
  }

  if (!orderId || typeof orderId !== "string") {
    throwInvalidOrderId();
  }

  if (
    secondaryMarketPrice === null ||
    secondaryMarketPrice === undefined ||
    typeof secondaryMarketPrice !== "number" ||
    secondaryMarketPrice <= 0
  ) {
    throwInvalidSecondaryPrice();
  }

  try {
    logSetSecondaryPriceStarted({ orderId, userId, secondaryMarketPrice });

    const result = await setSecondaryPriceService({
      orderId,
      secondaryMarketPrice,
      userId,
    });

    logSetSecondaryPriceSuccess({ orderId, userId, secondaryMarketPrice });

    return result;
  } catch (error) {
    logSecondaryMarketError({
      error,
      operation: "set_secondary_price",
      orderId,
      userId,
    });
    throwSecondaryMarketInternalError((error as any).message);
  }
});

export const makeSecondaryMarketPurchase = onCall<{
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId } = request.data;
  const newBuyerId = request.auth?.uid;

  if (!newBuyerId) {
    throwUnauthenticated();
  }

  if (!orderId || typeof orderId !== "string") {
    throwInvalidOrderId();
  }

  try {
    logSecondaryPurchaseStarted({
      orderId,
      newBuyerId,
      oldBuyerId: "unknown", // Will be determined in service
      secondaryMarketPrice: 0, // Will be determined in service
    });

    const result = await makeSecondaryPurchaseService({
      orderId,
      newBuyerId,
    });

    logSecondaryPurchaseSuccess({
      orderId: result.orderId,
      newBuyerId: result.newBuyerId,
      oldBuyerId: result.oldBuyerId,
      secondaryMarketPrice: result.secondaryMarketPrice,
      feeAmount: result.feeAmount,
      lockedAmount: result.lockedAmount,
    });

    return result;
  } catch (error) {
    logSecondaryMarketError({
      error,
      operation: "secondary_purchase",
      orderId,
      userId: newBuyerId,
    });
    throwSecondaryMarketInternalError((error as any).message);
  }
});
