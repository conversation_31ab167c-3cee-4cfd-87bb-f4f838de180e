import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateBuyerOwnership,
  validateOrderCreationParams,
  validatePurchaseParams,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { processPurchase } from "../../services/purchase-flow-service";

export interface CreateOrderAsBuyerParams {
  buyerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsBuyerParams {
  buyerId: string;
  orderId: string;
}

export function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsBuyerParams
): any {
  const authRequest = requireAuthentication(request);
  validateOrderCreationParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);
  return authRequest;
}

export function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsBuyerParams
): any {
  const authRequest = requireAuthentication(request);
  validatePurchaseParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);
  return authRequest;
}

export async function createBuyerOrder(
  params: CreateOrderAsBuyerParams
): Promise<any> {
  const db = admin.firestore();
  const { buyerId, collectionId, price } = params;

  return await createOrder(db, {
    userId: buyerId,
    collectionId,
    price,
    gift: null,
    userType: UserType.BUYER,
    secondaryMarketPrice: null,
  });
}

export async function processBuyerPurchase(
  params: MakePurchaseAsBuyerParams
): Promise<any> {
  const db = admin.firestore();
  const { buyerId, orderId } = params;

  return await processPurchase(db, {
    userId: buyerId,
    orderId,
    userType: UserType.BUYER,
  });
}
