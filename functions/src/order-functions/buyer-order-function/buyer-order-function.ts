import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  logCreateOrderError,
  logPurchaseError,
} from "./buyer-order-function.logger";
import { BuyerOrderFunctionErrorHandler } from "./buyer-order-function.error-handler";
import {
  createBuyerOrder,
  validatePurchaseRequest,
  processBuyerPurchase,
  validateCreateOrderRequest,
} from "./buyer-order-function.service";

export const createOrderAsBuyer = onCall<{
  buyerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, collectionId, price } = request.data;

  try {
    validateCreateOrderRequest(request, {
      buyerId,
      collectionId,
      price,
    });

    const result = await createBuyerOrder({
      buyerId,
      collectionId,
      price,
    });

    return result;
  } catch (error) {
    logCreateOrderError({
      error,
      operation: "create_order_as_buyer",
      requestData: request.data,
      userId: request.auth?.uid,
    });

    BuyerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsBuyer = onCall<{
  buyerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, orderId } = request.data;

  try {
    validatePurchaseRequest(request, {
      buyerId,
      orderId,
    });

    const result = await processBuyerPurchase({
      buyerId,
      orderId,
    });

    return result;
  } catch (error) {
    logPurchaseError({
      error,
      buyerId,
      orderId,
      operation: "buyer_purchase",
    });

    BuyerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
