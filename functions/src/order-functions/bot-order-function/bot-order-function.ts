import { onCall } from "firebase-functions/v2/https";
import { OrderGift } from "../../miker<PERSON>nko/marketplace-shared";
import { commonFunctionsConfig } from "../../constants";
import {
  getOrderById,
  getUserOrders,
  completePurchase,
  sendGiftToRelayer,
} from "./bot-order-function.service";
import {
  logGetOrderError,
  logGetUserOrdersError,
  logCompletePurchaseError,
  logSendGiftError,
} from "./bot-order-function.logger";
import { throwBotOrderInternalError } from "./bot-order-function.error-handler";

export const getOrderByIdByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  try {
    return await getOrderById({ orderId, botToken });
  } catch (error) {
    logGetOrderError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const getUserOrdersByBot = onCall<{
  userId?: string;
  tgId?: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, tgId, botToken } = request.data;

  try {
    return await getUserOrders({ userId, tgId, botToken });
  } catch (error) {
    logGetUserOrdersError({
      error,
      userId: request.data.userId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const completePurchaseByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  try {
    return await completePurchase({ orderId, botToken });
  } catch (error) {
    logCompletePurchaseError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const sendGiftToRelayerByBot = onCall<{
  orderId: string;
  gift: OrderGift;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, gift, botToken } = request.data;

  try {
    return await sendGiftToRelayer({ orderId, gift, botToken });
  } catch (error) {
    logSendGiftError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});
