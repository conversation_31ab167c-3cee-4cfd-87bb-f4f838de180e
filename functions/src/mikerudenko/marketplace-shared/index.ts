/* eslint-disable no-unused-vars */
import { Timestamp } from "firebase-admin/firestore";

export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export type AppDate = Date | Timestamp;

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface Withdrawal24h {
  amount: number; // Total amount withdrawn in the last 24 hours
  lastResetAt: AppDate; // Timestamp when the 24h period started
}

export interface UserEntity {
  id: string;
  name?: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role?: Role;
  tg_id?: string;
  telegram_handle?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string; // Telegram ID of the user who referred this user
  referral_fee?: number; // Custom referral fee in BPS (basis points)
  points?: number; // Points earned from referrals
  balance?: UserBalance;
  withdrawal_24h?: Withdrawal24h; // 24-hour withdrawal tracking
}

// Collection-related types
export enum CollectionStatus {
  PREMARKET = "PREMARKET",
  MARKET = "MARKET",
  DELETED = "DELETED",
}

export interface CollectionEntity {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: AppDate;
  floorPrice: number; // Minimum floor price for collection items in TON
  active: boolean; // Whether the collection is active for order creation
  lock_period?: number; // Lock period in days for this collection (overrides app config)
}

// Order-related types
export enum OrderStatus {
  CREATED = "created",
  ACTIVE = "active",
  PAID = "paid",
  GIFT_SENT_TO_RELAYER = "gift_sent_to_relayer",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderFees {
  buyer_locked_percentage: number; // in BPS
  seller_locked_percentage: number; // in BPS
  purchase_fee: number; // in BPS
  referrer_fee: number; // in BPS
  order_cancellation_fee: number; // in BPS
  resell_purchase_fee: number; // in BPS
  resell_purchase_fee_for_seller: number; // in BPS
}

export type OrderGift = {
  base_name: string;
  owned_gift_id: string;
  backdrop: {
    name: string;
    colors: {
      center_color: number;
      edge_color: number;
      symbol_color: number;
      text_color: number;
    };
    rarity_per_mille: number;
  };
  model: {
    name: string;
    rarity_per_mille: number;
  };
  symbol: {
    name: string;
    rarity_per_mille: number;
  };
};

export interface OrderEntity {
  id?: string;
  number: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId?: string; // Optional since orders can be created without a seller
  collectionId: string; // Collection ID for floor price validation
  price: number;
  status: OrderStatus;
  deadline?: AppDate; // Deadline for seller to fulfill the order
  giftSentToRelayerAt?: AppDate;
  // @deprecated
  secondaryMarketPrice?: number | null; // Price set by buyer for reselling on secondary market
  reseller_earnings_for_seller?: number; // Accumulated earnings for seller from resells
  fees?: OrderFees; // Fees snapshot from app_config at order creation time
  createdAt?: AppDate;
  updatedAt?: AppDate;
  gift?: OrderGift | null;
}

export interface ResellTxHistoryEntity {
  id?: string;
  order_id: string;
  execution_price: string; // TON amount as string
  reseller_id: string; // user who resold the order
  executed_at?: AppDate;
  buyer_id: string; // user who bought the resold order
}

// Transaction-related types
export enum TxType {
  DEPOSIT = "deposit",
  WITHDRAW = "withdraw",
  BUY_LOCK_COLLATERAL = "buy_lock_collateral",
  UNLOCK_COLLATERAL = "unlock_collateral",
  SELL_LOCK_COLLATERAL = "sell_lock_collateral",
  REFERRAL_FEE = "referral_fee",
  CANCELATION_FEE = "cancelation_fee",
  REFUND = "refund",
  SELL_FULFILLMENT = "sell_fulfillment",
  RESELL_FEE_EARNINGS = "resell_fee_earnings",
}

export interface UserTxEntity {
  id?: string;
  tx_type: TxType;
  user_id: string;
  amount: number; // can be positive or negative
  order_id?: string; // optional reference to related order
  description?: string; // optional description for the transaction
  createdAt?: AppDate;
}

export interface TxLookupEntity {
  id?: string;
  last_checked_record_id: string;
  updatedAt?: AppDate;
}

// App config types
export interface AppConfigEntity {
  id?: string;
  deposit_fee: number; // Static TON value
  withdrawal_fee: number; // Static TON value
  referrer_fee: number; // in BPS (basis points)
  cancel_order_fee: number; // in BPS - dynamic fee for two-person order cancellations
  purchase_fee: number; // in BPS
  buyer_lock_percentage: number; // in BPS (basis points)
  seller_lock_percentage: number; // in BPS (basis points)
  resell_purchase_fee: number; // in BPS - fee applied to secondary market purchases
  resell_purchase_fee_for_seller: number; // in BPS - fee for seller on each resell
  min_deposit_amount: number; // Static TON value
  min_withdrawal_amount: number; // Static TON value
  max_withdrawal_amount: number; // Static TON value
  min_secondary_market_price: number; // Minimum price for secondary market resale in TON
  fixed_cancel_order_fee: number; // Static TON value for single-person order cancellations
  lock_period: number; // Default lock period in days for collections
}

export enum UserType {
  BUYER = "buyer",
  SELLER = "seller",
}

export const APP_CONFIG_COLLECTION = "app_config";
export const APP_CONFIG_DOC_ID = "main";
export const APP_USERS_COLLECTION = "users";
export const MARKETPLACE_REVENUE_USER_ID = "marketplace_revenue";
export const COLLECTION_NAME = "collections";
export const ORDERS_COLLECTION_NAME = "orders";
export const COUNTERS_COLLECTION_NAME = "counters";

export const REFERRAL_TIERS = [
  { friends: 3, points: 50 },
  { friends: 5, points: 100 },
  { friends: 10, points: 150 },
  { friends: 25, points: 300 },
  { friends: 50, points: 500 },
];

export const firebaseTimestampToDate = (
  timestamp?: AppDate | null | string
): Date => {
  if (timestamp instanceof Date) {
    return timestamp;
  }

  if (!timestamp) {
    return new Date();
  }

  if (typeof timestamp === "string") {
    return new Date(timestamp);
  }

  return timestamp.toDate();
};

export const formatDateToFirebaseTimestamp = (
  date?: AppDate | null | string
): Timestamp => {
  if (date instanceof Timestamp) {
    return date;
  }

  if (!date) {
    return Timestamp.now();
  }

  if (typeof date === "string") {
    return Timestamp.fromDate(new Date(date));
  }

  return Timestamp.fromDate(date);
};

export interface BotSessionEntity {
  id: string;
  pendingOrderId?: string;
  echoMode?: boolean;
  createdAt?: AppDate;
  updatedAt?: AppDate;
}
