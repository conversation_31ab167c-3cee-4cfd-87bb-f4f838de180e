{"name": "@mikerudenko/marketplace-shared", "version": "0.0.3", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "prepare": "npm run build", "postinstall": "npm run build"}, "dependencies": {"@firebase/firestore": "^4.9.0", "firebase": "^12.0.0", "firebase-admin": "^13.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "typescript": "^5.8.3"}}